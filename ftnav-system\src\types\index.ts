// 导航项接口
export interface NavigationItem {
  id: string;
  title: string;
  description?: string;
  url: string;
  icon: string;
  category: string;
  tags?: string[];
  isFavorite?: boolean;
  createdAt: string;
  updatedAt: string;
}

// 数据中心接口
export interface DataCenter {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  count?: number;
}

// 导航分类接口
export interface NavigationCategory {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  parentId?: string;
  order: number;
}

// API响应接口
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 搜索筛选参数
export interface SearchFilters {
  keyword?: string;
  category?: string;
  dataCenter?: string;
  tags?: string[];
  isFavorite?: boolean;
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  viewMode: 'grid' | 'list' | 'compact';
  pageSize: number;
  defaultDataCenter?: string;
} 