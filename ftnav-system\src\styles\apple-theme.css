/* 应用商店风格主题样式 */
:root {
  /* 基础色彩系统 */
  --color-primary: #1677ff;
  --color-primary-hover: #4096ff;
  --color-success: #52c41a;
  --color-warning: #faad14;
  --color-error: #ff4d4f;
  
  /* 背景色系 */
  --color-bg-base: #ffffff;
  --color-bg-layout: #f5f5f5;
  --color-bg-container: #ffffff;
  --color-bg-elevated: #ffffff;
  --color-bg-spotlight: #fafafa;
  
  /* 文本色系 */
  --color-text-primary: #262626;
  --color-text-secondary: #8c8c8c;
  --color-text-tertiary: #bfbfbf;
  --color-text-light: #ffffff;
  
  /* 边框和分割线 */
  --color-border: #e8e8e8;
  --color-border-light: #f0f0f0;
  --color-divider: #f0f0f0;
  
  /* 阴影系统 */
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-card-hover: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-elevated: 0 6px 24px rgba(0, 0, 0, 0.15);
  
  /* 圆角系统 */
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-xxl: 48px;
  
  /* 字体系统 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1600px;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: var(--color-bg-layout);
  color: var(--color-text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 主布局样式 */
.app-layout {
  min-height: 100vh;
  background: var(--color-bg-layout);
}

.app-layout .ant-layout-sider {
  background: var(--color-bg-container) !important;
  border-right: 1px solid var(--color-border-light);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.02);
}

.app-layout .ant-layout-content {
  background: var(--color-bg-layout);
  padding: 0;
}

/* 侧边栏样式 */
.sidebar-container {
  height: 100vh;
  overflow-y: auto;
  background: var(--color-bg-container);
}

.sidebar-header {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--color-border-light);
  background: var(--color-bg-container);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
}

.sidebar-brand-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: white;
  border-radius: var(--border-radius-md);
  font-size: 16px;
}

.sidebar-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.sidebar-content {
  padding: var(--space-sm) var(--space-md);
}

.sidebar-section {
  margin-bottom: var(--space-lg);
}

.sidebar-section-title {
  font-size: var(--font-size-xs);
  font-weight: 500;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-sm);
  padding: 0 var(--space-sm);
}

/* 侧边栏菜单项 */
.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  margin-bottom: 2px;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--color-text-primary);
  position: relative;
  min-height: 40px;
}

.sidebar-menu-item:hover {
  background: var(--color-bg-spotlight);
  color: var(--color-primary);
  transform: translateX(2px);
}

.sidebar-menu-item.active {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: var(--color-text-light);
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

.sidebar-menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: white;
  border-radius: 0 2px 2px 0;
}

.sidebar-menu-item-icon {
  margin-right: var(--space-sm);
  font-size: var(--font-size-base);
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-menu-item-text {
  flex: 1;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.sidebar-menu-item-count {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  background: var(--color-bg-layout);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  min-width: 18px;
  text-align: center;
  font-weight: 500;
}

.sidebar-menu-item.active .sidebar-menu-item-count {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-text-light);
}

/* 主内容区域 */
.main-content {
  background: var(--color-bg-layout);
  min-height: 100vh;
  position: relative;
}

.main-header {
  background: var(--color-bg-container);
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--color-border-light);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.main-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.main-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.main-header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.main-search {
  width: 280px;
}

.main-search .ant-input {
  border-radius: var(--border-radius-lg);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  border: 1px solid var(--color-border);
  height: 36px;
}

.main-search .ant-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 主要按钮样式 */
.primary-button {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover)) !important;
  border: none !important;
  border-radius: var(--border-radius-lg) !important;
  padding: var(--space-sm) var(--space-lg) !important;
  font-weight: 500 !important;
  height: auto !important;
  line-height: 1.4 !important;
}

.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-card-hover) !important;
}

/* 工具栏样式 */
.main-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-sm);
  padding: 0;
}

.toolbar-filters {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

/* 内容网格 */
.content-grid {
  padding: var(--space-lg) var(--space-lg) var(--space-xl);
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-md);
}

/* 应用商店风格的导航卡片 */
.store-card {
  background: var(--color-bg-container);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--space-md) var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: fit-content;
}

.store-card:hover {
  border-color: var(--color-primary);
  box-shadow: 0 8px 32px rgba(22, 119, 255, 0.15);
  transform: translateY(-4px);
}

.store-card-main {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  margin-bottom: var(--space-sm);
}

.store-card-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
}

.store-card-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.store-card:hover .store-card-icon::before {
  transform: translateX(100%);
}

.store-card-content {
  flex: 1;
  min-width: 0;
}

.store-card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-xs) 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.store-card-author {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-sm);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.store-card-author-icon {
  color: var(--color-text-tertiary);
}

.store-card-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.store-card-actions {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.store-card:hover .store-card-actions {
  opacity: 1;
}

.store-card-action-btn {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-md);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--color-border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.store-card-action-btn:hover {
  background: var(--color-bg-container);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: scale(1.1);
}

.store-card-favorite.active {
  color: var(--color-error);
}

.store-card-visit-btn {
  border-radius: var(--border-radius-md) !important;
  font-weight: 500 !important;
  height: 32px !important;
  padding: 0 var(--space-md) !important;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover)) !important;
  border: none !important;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2) !important;
}

.store-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-md);
  padding-top: var(--space-md);
  border-top: 1px solid var(--color-border-light);
}

.store-card-stats {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.store-card-stat {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

.store-card-stat-icon {
  font-size: var(--font-size-sm);
}

.store-card-tags {
  display: flex;
  gap: var(--space-xs);
  flex-wrap: wrap;
  max-width: 200px;
}

.store-card-tag {
  font-size: var(--font-size-xs);
  padding: 2px var(--space-xs);
  background: var(--color-bg-spotlight);
  color: var(--color-text-secondary);
  border-radius: var(--border-radius-sm);
  border: none;
}

.store-card-tag.primary {
  background: rgba(22, 119, 255, 0.1);
  color: var(--color-primary);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: var(--space-xxl) var(--space-lg);
  color: var(--color-text-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: var(--color-bg-container);
  border-radius: var(--border-radius-xl);
  margin: var(--space-lg);
  border: 2px dashed var(--color-border-light);
  position: relative;
  overflow: hidden;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.03) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-state-icon {
  font-size: 80px;
  color: var(--color-text-tertiary);
  margin-bottom: var(--space-lg);
  opacity: 0.6;
  position: relative;
  z-index: 1;
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  position: relative;
  z-index: 1;
}

.empty-state-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  max-width: 400px;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-xxl);
}

/* 移动端特殊样式 */
.mobile-menu-toggle {
  position: fixed !important;
  top: var(--space-md) !important;
  left: var(--space-md) !important;
  z-index: 1001 !important;
  width: 44px !important;
  height: 44px !important;
  border-radius: var(--border-radius-lg) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid var(--color-border-light) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.mobile-menu-toggle:hover {
  background: var(--color-bg-container) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-primary) !important;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .navigation-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-md);
  }
}

@media (max-width: 1400px) {
  .navigation-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-md);
  }
}

@media (max-width: 1200px) {
  .navigation-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-md);
  }

  .main-search {
    width: 240px;
  }

  .content-grid {
    padding: var(--space-md) var(--space-lg);
  }
}

@media (max-width: 1024px) {
  .navigation-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-md);
  }

  .main-header {
    padding: var(--space-sm) var(--space-md);
  }

  .main-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: var(--space-sm) var(--space-md);
  }

  .main-header-top {
    flex-direction: column;
    gap: var(--space-sm);
    align-items: stretch;
  }

  .main-header-actions {
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .main-search {
    width: 100%;
    max-width: none;
    order: -1;
  }

  .main-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
  }

  .toolbar-filters {
    justify-content: center;
    flex-wrap: wrap;
  }

  .navigation-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .content-grid {
    padding: var(--space-md);
  }

  .store-card {
    padding: var(--space-md);
  }

  .store-card-main {
    gap: var(--space-sm);
  }

  .store-card-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }

  .store-card-actions {
    position: static;
    opacity: 1;
    margin-top: var(--space-sm);
    justify-content: flex-end;
  }

  .store-card-footer {
    flex-direction: column;
    gap: var(--space-sm);
    align-items: flex-start;
  }

  .empty-state {
    margin: var(--space-sm);
    padding: var(--space-xl) var(--space-md);
    min-height: 300px;
  }

  .empty-state-icon {
    font-size: 64px;
  }

  .empty-state-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .content-grid {
    padding: var(--space-sm);
  }

  .main-header {
    padding: var(--space-xs) var(--space-sm);
  }

  .main-title {
    font-size: var(--font-size-base);
  }

  .store-card {
    padding: var(--space-sm);
    border-radius: var(--border-radius-md);
  }

  .store-card-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .store-card-title {
    font-size: var(--font-size-sm);
  }

  .store-card-stats {
    gap: var(--space-sm);
  }

  .empty-state {
    margin: var(--space-xs);
    padding: var(--space-lg) var(--space-sm);
    min-height: 250px;
  }

  .empty-state-icon {
    font-size: 48px;
  }

  .empty-state-title {
    font-size: var(--font-size-base);
  }

  .empty-state-description {
    font-size: var(--font-size-sm);
  }
}