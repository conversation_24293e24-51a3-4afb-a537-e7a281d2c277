# FinTech Nav - 金融科技导航系统

一个现代化的企业级导航管理系统，采用应用商店风格的卡片设计，为金融科技团队提供统一的系统入口和资源管理。

## ✨ 特性

### 🎨 现代化界面设计
- **应用商店风格**：采用类似 商店卡片的设计，直观美观
- **扁平化设计**：低饱和度配色，简洁的按钮设计，减少视觉干扰
- **响应式布局**：完美适配桌面端设备
- **一致性体验**：统一的设计语言，自定义滚动条与页面主题融合
- **流畅动画**：精心设计的交互动画，提供丝滑的操作体验

### 📱 智能响应式布局
- **PC端优化**：专注桌面端体验，智能网格布局自动适应屏幕宽度
- **多分辨率支持**：
  - 超大屏（1600px+）：4-6列自适应显示
  - 大屏（1200px-1599px）：3-4列自适应显示
  - 中屏（900px-1199px）：2-3列自适应显示
- **空间利用最大化**：使用 `auto-fit` 网格布局，充分利用可用空间
- **流畅响应**：平滑的断点过渡，保持视觉一致性

### 🔍 强大的管理功能
- **智能搜索**：支持名称、描述、标签的全文搜索
- **分类管理**：灵活的分类系统，支持自定义分类和颜色
- **收藏功能**：一键收藏常用系统，快速访问
- **统计信息**：显示访问量、点赞数等使用统计

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🛠️ 技术栈

- **前端框架**：React 19 + TypeScript
- **构建工具**：Vite 7
- **UI 组件库**：Ant Design 5
- **状态管理**：Zustand
- **图标库**：Ant Design Icons + Lucide React
- **样式方案**：CSS Variables + 响应式设计


## 🎨 界面预览

### 桌面端
- **宽屏布局**：侧边栏 + 主内容区的经典布局
- **卡片网格**：自适应的卡片网格，最大化利用屏幕空间
- **悬停效果**：丰富的交互反馈

## 🔧 自定义配置

### 主题定制
系统使用 CSS 变量进行主题管理，可以轻松自定义颜色、间距、圆角等：

```css
:root {
  --color-primary:rgb(137, 184, 249);
  --color-primary-hover:rgb(131, 183, 247);
  --border-radius-lg: 12px;
  /* ... 更多变量 */
}
```

### 响应式断点
```css
--breakpoint-xs: 480px;
--breakpoint-sm: 768px;
--breakpoint-md: 1024px;
--breakpoint-lg: 1280px;
--breakpoint-xl: 1600px;
```


