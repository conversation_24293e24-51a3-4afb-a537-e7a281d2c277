# FinTech Nav - 金融科技导航系统

一个现代化的企业级导航管理系统，采用应用商店风格的卡片设计，为金融科技团队提供统一的系统入口和资源管理。

## ✨ 特性

### 🎨 现代化界面设计
- **应用商店风格**：采用类似 App Store 的卡片设计，直观美观
- **扁平化设计**：低饱和度配色，简洁的按钮设计，减少视觉干扰
- **响应式布局**：完美适配桌面端、平板和移动设备
- **一致性体验**：统一的设计语言，自定义滚动条与页面主题融合
- **流畅动画**：精心设计的交互动画，提供丝滑的操作体验

### 📱 智能响应式布局
- **PC端优化**：专注桌面端体验，智能网格布局自动适应屏幕宽度
- **多分辨率支持**：
  - 超大屏（1600px+）：4-6列自适应显示
  - 大屏（1200px-1599px）：3-4列自适应显示
  - 中屏（900px-1199px）：2-3列自适应显示
- **空间利用最大化**：使用 `auto-fit` 网格布局，充分利用可用空间
- **流畅响应**：平滑的断点过渡，保持视觉一致性

### 🔍 强大的管理功能
- **智能搜索**：支持名称、描述、标签的全文搜索
- **分类管理**：灵活的分类系统，支持自定义分类和颜色
- **收藏功能**：一键收藏常用系统，快速访问
- **统计信息**：显示访问量、点赞数等使用统计

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🛠️ 技术栈

- **前端框架**：React 19 + TypeScript
- **构建工具**：Vite 7
- **UI 组件库**：Ant Design 5
- **状态管理**：Zustand
- **图标库**：Ant Design Icons + Lucide React
- **样式方案**：CSS Variables + 响应式设计

## 📁 项目结构

```
ftnav-system/
├── src/
│   ├── components/          # React 组件
│   │   ├── Sidebar.tsx     # 侧边栏组件
│   │   ├── MainContent.tsx # 主内容区组件
│   │   ├── NavigationCard.tsx # 导航卡片组件
│   │   └── ...
│   ├── store/              # 状态管理
│   │   └── navigationStore.ts
│   ├── styles/             # 样式文件
│   │   └── apple-theme.css
│   ├── types/              # TypeScript 类型定义
│   └── ...
├── public/                 # 静态资源
└── ...
```

## 🎨 界面预览

### 桌面端
- **宽屏布局**：侧边栏 + 主内容区的经典布局
- **卡片网格**：自适应的卡片网格，最大化利用屏幕空间
- **悬停效果**：丰富的交互反馈

### 移动端
- **抽屉式侧边栏**：节省屏幕空间的抽屉式导航
- **单列布局**：移动端优化的单列卡片布局
- **触摸优化**：适合触摸操作的按钮和间距

## 🔧 自定义配置

### 主题定制
系统使用 CSS 变量进行主题管理，可以轻松自定义颜色、间距、圆角等：

```css
:root {
  --color-primary: #1677ff;
  --color-primary-hover: #4096ff;
  --border-radius-lg: 12px;
  /* ... 更多变量 */
}
```

### 响应式断点
```css
--breakpoint-xs: 480px;
--breakpoint-sm: 768px;
--breakpoint-md: 1024px;
--breakpoint-lg: 1280px;
--breakpoint-xl: 1600px;
```

## 📝 更新日志

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的版本更新记录。

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
