import React, { useState } from 'react';
import { Layout, <PERSON>u, But<PERSON>, Badge, Tooltip } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  AppstoreOutlined,
  HeartFilled,
  PlusOutlined,
  SettingOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { useNavigationStore } from '../store/navigationStore';
import { IconComponent } from './IconComponent';

const { Sider } = Layout;

interface SidebarProps {
  onAddNavigation?: () => void;
  onManageCategories?: () => void;
  onManageDataCenters?: () => void;
  showMobileSidebar?: boolean;
  onCloseMobileSidebar?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  onAddNavigation,
  onManageCategories,
  onManageDataCenters,
  showMobileSidebar,
  onCloseMobileSidebar,
}) => {
  const {
    userPreferences,
    categories,
    dataCenters,
    navigationItems,
    toggleSidebar,
    setSearchFilters,
  } = useNavigationStore();

  const [selectedKeys, setSelectedKeys] = useState<string[]>(['all']);

  const getCategoryCount = (categoryId: string) => {
    return navigationItems.filter(item => item.category === categoryId).length;
  };

  const getFavoriteCount = () => {
    return navigationItems.filter(item => item.isFavorite).length;
  };

  const handleMenuSelect = ({ key }: { key: string }) => {
    setSelectedKeys([key]);
    
    switch (key) {
      case 'all':
        setSearchFilters({ category: undefined, isFavorite: undefined });
        break;
      case 'favorites':
        setSearchFilters({ isFavorite: true, category: undefined });
        break;
      default:
        if (key.startsWith('cat-')) {
          const categoryId = key.replace('cat-', '');
          setSearchFilters({ category: categoryId, isFavorite: undefined });
        } else if (key.startsWith('dc-')) {
          const dataCenterId = key.replace('dc-', '');
          setSearchFilters({ dataCenter: dataCenterId, isFavorite: undefined });
        }
        break;
    }
  };

  const menuItems = [
    {
      key: 'all',
      icon: <AppstoreOutlined />,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>全部导航</span>
          <Badge count={navigationItems.length} size="small" />
        </div>
      ),
    },
    {
      key: 'favorites',
      icon: <HeartFilled style={{ color: '#ff4d4f' }} />,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>收藏夹</span>
          <Badge count={getFavoriteCount()} size="small" />
        </div>
      ),
    },
    { type: 'divider' as const },
    {
      key: 'categories-group',
      type: 'group' as const,
      label: '分类',
      children: categories.map(category => ({
        key: `cat-${category.id}`,
        icon: <IconComponent name={category.icon} size={16} color={category.color} />,
        label: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{category.name}</span>
            <Badge count={getCategoryCount(category.id)} size="small" />
          </div>
        ),
      })),
    },
  ];

  return (
    <Sider
      className={`sidebar ${showMobileSidebar ? 'sidebar-open' : ''}`}
      trigger={null}
      collapsible
      collapsed={userPreferences.sidebarCollapsed}
      width={240}
      collapsedWidth={64}
    >
      <div className="sidebar-header">
        <div className="sidebar-brand">
          <div className="sidebar-brand-icon">
            <DatabaseOutlined />
          </div>
          {!userPreferences.sidebarCollapsed && <span>FinTech Nav</span>}
        </div>
        
        <Button
          type="text"
          icon={userPreferences.sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={toggleSidebar}
        />
      </div>

      {!userPreferences.sidebarCollapsed && (
        <div style={{ padding: '16px' }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            block
            size="large"
            onClick={onAddNavigation}
          >
            添加导航
          </Button>
        </div>
      )}

      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        items={menuItems}
        onSelect={handleMenuSelect}
      />
    </Sider>
  );
};

export default Sidebar; 