import React, { useState, useMemo, useEffect } from 'react';
import { Layout, Input, Button, Space, Select, Badge, Empty, Spin } from 'antd';
import { 
  SearchOutlined, 
  AppstoreOutlined, 
  UnorderedListOutlined,
  FilterOutlined,
  PlusOutlined,
  MenuOutlined
} from '@ant-design/icons';
import { useNavigationStore } from '../store/navigationStore';
import NavigationCard from './NavigationCard';
import NavigationForm from './NavigationForm';
import type { NavigationItem } from '../types';

const { Content } = Layout;
const { Search } = Input;

interface MainContentProps {
  onAddNavigation?: () => void;
  showMobileSidebar?: boolean;
  onToggleMobileSidebar?: () => void;
}

/**
 * 主内容区组件
 * 包含搜索、筛选、导航卡片展示等功能
 */
export const MainContent: React.FC<MainContentProps> = ({ 
  onAddNavigation, 
  showMobileSidebar, 
  onToggleMobileSidebar 
}) => {
  const {
    searchFilters,
    userPreferences,
    categories,
    loading,
    setSearchFilters,
    setViewMode,
    getFilteredNavigationItems,
  } = useNavigationStore();

  const [editingItem, setEditingItem] = useState<NavigationItem | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 获取筛选后的导航项
  const filteredItems = useMemo(() => {
    return getFilteredNavigationItems();
  }, [getFilteredNavigationItems]);

  // 获取当前显示的标题
  const getDisplayTitle = () => {
    if (searchFilters.isFavorite) {
      return '收藏夹';
    }
    if (searchFilters.category) {
      const category = categories.find(cat => cat.id === searchFilters.category);
      return category?.name || '分类导航';
    }
    if (searchFilters.keyword) {
      return `搜索结果："${searchFilters.keyword}"`;
    }
    return '全部导航';
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchFilters({ keyword: value || undefined });
  };

  // 处理编辑导航项
  const handleEditItem = (item: NavigationItem) => {
    setEditingItem(item);
    setShowForm(true);
  };

  // 处理添加导航
  const handleAddNavigation = () => {
    setEditingItem(null);
    setShowForm(true);
    onAddNavigation?.();
  };

  // 处理表单关闭
  const handleFormCancel = () => {
    setShowForm(false);
    setEditingItem(null);
  };

  // 分类选项
  const categoryOptions = [
    { label: '全部分类', value: undefined },
    ...categories.map(cat => ({
      label: cat.name,
      value: cat.id,
    })),
  ];

  if (loading) {
    return (
      <Content style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        height: '50vh' 
      }}>
        <Spin size="large" tip="加载中..." />
      </Content>
    );
  }

  return (
    <Content className="main-content">
      {/* 移动端菜单按钮 */}
      {isMobile && (
        <Button
          className="mobile-menu-toggle"
          icon={<MenuOutlined />}
          onClick={onToggleMobileSidebar}
          size="large"
        />
      )}
      
      {/* 移动端侧边栏遮罩 */}
      {isMobile && showMobileSidebar && (
        <div 
          className="sidebar-overlay active"
          onClick={onToggleMobileSidebar}
        />
      )}

      {/* 头部区域 */}
      <div className="main-header">
        <div className="main-header-top">
          <h1 className="main-title">{getDisplayTitle()}</h1>
          
          <div className="main-header-actions">
            {/* 搜索框 */}
            <Search
              placeholder="搜索导航项..."
              allowClear
              onSearch={handleSearch}
              className="main-search"
              size="large"
            />


          </div>
        </div>

        <div className="main-toolbar">
          <div className="toolbar-filters">
            <span style={{ color: 'var(--color-text-secondary)', fontSize: '14px' }}>
              共找到 {filteredItems.length} 个导航项
              {searchFilters.keyword && (
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => setSearchFilters({ keyword: undefined })}
                  style={{ padding: 0, marginLeft: 8 }}
                >
                  清除搜索
                </Button>
              )}
            </span>

            {/* 分类筛选 */}
            <Select
              placeholder="选择分类"
              allowClear
              value={searchFilters.category}
              onChange={(value) => setSearchFilters({ category: value })}
              options={categoryOptions}
              style={{ width: 160 }}
            />
          </div>

          <div className="toolbar-actions">
            {/* 视图切换 */}
            <Space.Compact>
              <Button
                type={userPreferences.viewMode === 'grid' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setViewMode('grid')}
              />
              <Button
                type={userPreferences.viewMode === 'list' ? 'primary' : 'default'}
                icon={<UnorderedListOutlined />}
                onClick={() => setViewMode('list')}
              />
            </Space.Compact>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="content-grid">
        {filteredItems.length === 0 ? (
          /* 空状态 */
          <div className="empty-state">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <div className="empty-state-title">
                    {searchFilters.keyword ? '未找到相关导航' : '暂无导航项'}
                  </div>
                  <div className="empty-state-description">
                    {searchFilters.keyword
                      ? '尝试使用不同的关键词搜索'
                      : '点击左侧边栏的"添加导航"按钮来创建第一个导航项'
                    }
                  </div>
                </div>
              }
            />
          </div>
        ) : (
          /* 导航卡片网格 */
          <div className="navigation-grid">
            {filteredItems.map((item) => (
              <NavigationCard
                key={item.id}
                item={item}
                onEdit={handleEditItem}
              />
            ))}
          </div>
        )}
      </div>

      {/* 导航表单弹窗 */}
      <NavigationForm
        visible={showForm}
        onCancel={handleFormCancel}
        editItem={editingItem}
      />


    </Content>
  );
};

export default MainContent; 