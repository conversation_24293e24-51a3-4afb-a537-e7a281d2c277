import React, { useEffect } from 'react';
import { ConfigProvider, Layout, message } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useNavigationStore } from './store/navigationStore';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import './styles/apple-theme.css';

const { Content } = Layout;

/**
 * FinTech导航系统主应用组件
 * 集成了侧边栏、主内容区域和所有功能模块
 */
function App() {
  const { initializeData, navigationItems } = useNavigationStore();
  const [showMobileSidebar, setShowMobileSidebar] = React.useState(false);

  // 初始化应用数据
  useEffect(() => {
    // 如果没有数据，则初始化示例数据
    if (navigationItems.length === 0) {
      initializeData();
      message.success('欢迎使用 FinTech Nav 导航系统！', 2);
    }
  }, [initializeData, navigationItems.length]);

  return (
    <ConfigProvider 
      locale={zhCN}
      theme={{
        token: {
          // 自定义主题配色
          colorPrimary: '#1E3A8A',
          borderRadius: 12,
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
        },
        components: {
          Layout: {
            bodyBg: '#F8FAFC',
            siderBg: 'rgba(255, 255, 255, 0.95)',
          },
          Menu: {
            itemBg: 'transparent',
            itemSelectedBg: 'linear-gradient(135deg, #1E3A8A, #3B82F6)',
            itemHoverBg: 'rgba(30, 58, 138, 0.08)',
            itemSelectedColor: '#ffffff',
          },
          Button: {
            borderRadius: 8,
            controlHeight: 40,
          },
          Card: {
            borderRadius: 16,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1)',
          },
          Input: {
            borderRadius: 12,
            controlHeight: 44,
          },
          Select: {
            borderRadius: 12,
            controlHeight: 44,
          },
        },
      }}
    >
      <Layout style={{ minHeight: '100vh', background: '#F8FAFC' }}>
        {/* 侧边栏 */}
        <Sidebar 
          showMobileSidebar={showMobileSidebar}
          onCloseMobileSidebar={() => setShowMobileSidebar(false)}
        />
        
        {/* 主内容区 */}
        <Layout style={{ background: '#F8FAFC' }}>
          <MainContent 
            showMobileSidebar={showMobileSidebar}
            onToggleMobileSidebar={() => setShowMobileSidebar(!showMobileSidebar)}
          />
        </Layout>

        {/* 全局样式增强 */}
        <style>{`
          /* 滚动条美化 */
          ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }
          
          ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
          }
          
          ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1E3A8A, #3B82F6);
            border-radius: 4px;
          }
          
          ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #3B82F6, #1E3A8A);
          }
          
          /* Ant Design 组件自定义 */
          .ant-layout {
            background: #F8FAFC !important;
          }
          
          .ant-layout-sider {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(0, 0, 0, 0.06) !important;
          }
          
          .ant-menu {
            background: transparent !important;
            border: none !important;
          }
          
          .ant-menu-item {
            border-radius: 8px !important;
            margin: 2px 0 !important;
            height: auto !important;
            line-height: 1.4 !important;
            padding: 8px 12px !important;
          }
          
          .ant-menu-item:hover {
            background: rgba(30, 58, 138, 0.08) !important;
          }
          
          .ant-menu-item-selected {
            background: linear-gradient(135deg, #1E3A8A, #3B82F6) !important;
            color: white !important;
          }
          
          .ant-menu-item-selected .anticon {
            color: white !important;
          }
          
          /* 卡片悬停效果增强 */
          .ant-card {
            transition: all 0.25s ease !important;
          }
          
          .ant-card:hover {
            transform: translateY(-4px) !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1) !important;
          }
          
          /* 按钮渐变效果 */
          .ant-btn-primary {
            background: linear-gradient(135deg, #1E3A8A, #3B82F6) !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
          }
          
          .ant-btn-primary:hover {
            background: linear-gradient(135deg, #3B82F6, #1E3A8A) !important;
            box-shadow: 0 6px 16px rgba(30, 58, 138, 0.4) !important;
            transform: translateY(-1px) !important;
          }
          
          /* 输入框焦点效果 */
          .ant-input:focus,
          .ant-select-focused .ant-select-selector {
            border-color: #1E3A8A !important;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1) !important;
          }
          
          /* 模态框样式 */
          .ant-modal {
            backdrop-filter: blur(10px);
          }
          
          .ant-modal-content {
            border-radius: 16px !important;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
          }
          
          /* 标签样式 */
          .ant-tag {
            border-radius: 6px !important;
            border: none !important;
            background: rgba(30, 58, 138, 0.1) !important;
            color: #1E3A8A !important;
          }
          
          /* Badge 样式 */
          .ant-badge-count {
            font-size: 10px !important;
            min-width: 16px !important;
            height: 16px !important;
            line-height: 14px !important;
            border-radius: 8px !important;
          }
          
          /* 空状态样式 */
          .ant-empty {
            color: #6B7280 !important;
          }
          
          .ant-empty-description {
            color: #9CA3AF !important;
          }
          
          /* 响应式调整 */
          @media (max-width: 768px) {
            .ant-layout-sider {
              position: fixed !important;
              left: 0 !important;
              top: 0 !important;
              z-index: 1000 !important;
              height: 100vh !important;
              transform: translateX(-100%) !important;
              transition: transform 0.3s ease !important;
            }

            .ant-layout-sider.sidebar-open {
              transform: translateX(0) !important;
            }

            .ant-layout-content {
              margin-left: 0 !important;
            }
          }
        `}</style>
      </Layout>
    </ConfigProvider>
  );
}

export default App;
