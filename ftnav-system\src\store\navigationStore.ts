import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { NavigationItem, DataCenter, NavigationCategory, SearchFilters, UserPreferences } from '../types';

interface NavigationStore {
  // 状态
  navigationItems: NavigationItem[];
  dataCenters: DataCenter[];
  categories: NavigationCategory[];
  searchFilters: SearchFilters;
  userPreferences: UserPreferences;
  loading: boolean;
  error: string | null;

  // 导航项操作
  addNavigationItem: (item: Omit<NavigationItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateNavigationItem: (id: string, updates: Partial<NavigationItem>) => void;
  deleteNavigationItem: (id: string) => void;
  toggleFavorite: (id: string) => void;
  setNavigationItems: (items: NavigationItem[]) => void;

  // 数据中心操作
  addDataCenter: (dataCenter: Omit<DataCenter, 'id' | 'count'>) => void;
  updateDataCenter: (id: string, updates: Partial<DataCenter>) => void;
  deleteDataCenter: (id: string) => void;
  setDataCenters: (dataCenters: DataCenter[]) => void;

  // 分类操作
  addCategory: (category: Omit<NavigationCategory, 'id'>) => void;
  updateCategory: (id: string, updates: Partial<NavigationCategory>) => void;
  deleteCategory: (id: string) => void;
  setCategories: (categories: NavigationCategory[]) => void;

  // 搜索和筛选
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  clearSearchFilters: () => void;
  getFilteredNavigationItems: () => NavigationItem[];

  // 用户偏好
  setUserPreferences: (preferences: Partial<UserPreferences>) => void;
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setViewMode: (mode: 'grid' | 'list' | 'compact') => void;

  // 状态操作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // 数据初始化
  initializeData: () => void;
}

// 生成ID的辅助函数
const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);

// 默认用户偏好
const defaultUserPreferences: UserPreferences = {
  theme: 'light',
  sidebarCollapsed: false,
  viewMode: 'grid',
  pageSize: 12,
};

export const useNavigationStore = create<NavigationStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      navigationItems: [],
      dataCenters: [],
      categories: [],
      searchFilters: {},
      userPreferences: defaultUserPreferences,
      loading: false,
      error: null,

      // 导航项操作
      addNavigationItem: (item) => {
        const newItem: NavigationItem = {
          ...item,
          id: generateId(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        set((state) => ({
          navigationItems: [...state.navigationItems, newItem],
        }));
      },

      updateNavigationItem: (id, updates) => {
        set((state) => ({
          navigationItems: state.navigationItems.map((item) =>
            item.id === id ? { ...item, ...updates, updatedAt: new Date().toISOString() } : item
          ),
        }));
      },

      deleteNavigationItem: (id) => {
        set((state) => ({
          navigationItems: state.navigationItems.filter((item) => item.id !== id),
        }));
      },

      toggleFavorite: (id) => {
        set((state) => ({
          navigationItems: state.navigationItems.map((item) =>
            item.id === id ? { ...item, isFavorite: !item.isFavorite, updatedAt: new Date().toISOString() } : item
          ),
        }));
      },

      setNavigationItems: (items) => set({ navigationItems: items }),

      // 数据中心操作
      addDataCenter: (dataCenter) => {
        const newDataCenter: DataCenter = {
          ...dataCenter,
          id: generateId(),
          count: 0,
        };
        set((state) => ({
          dataCenters: [...state.dataCenters, newDataCenter],
        }));
      },

      updateDataCenter: (id, updates) => {
        set((state) => ({
          dataCenters: state.dataCenters.map((dc) =>
            dc.id === id ? { ...dc, ...updates } : dc
          ),
        }));
      },

      deleteDataCenter: (id) => {
        set((state) => ({
          dataCenters: state.dataCenters.filter((dc) => dc.id !== id),
        }));
      },

      setDataCenters: (dataCenters) => set({ dataCenters }),

      // 分类操作
      addCategory: (category) => {
        const newCategory: NavigationCategory = {
          ...category,
          id: generateId(),
        };
        set((state) => ({
          categories: [...state.categories, newCategory],
        }));
      },

      updateCategory: (id, updates) => {
        set((state) => ({
          categories: state.categories.map((cat) =>
            cat.id === id ? { ...cat, ...updates } : cat
          ),
        }));
      },

      deleteCategory: (id) => {
        set((state) => ({
          categories: state.categories.filter((cat) => cat.id !== id),
        }));
      },

      setCategories: (categories) => set({ categories }),

      // 搜索和筛选
      setSearchFilters: (filters) => {
        set((state) => ({
          searchFilters: { ...state.searchFilters, ...filters },
        }));
      },

      clearSearchFilters: () => set({ searchFilters: {} }),

      getFilteredNavigationItems: () => {
        const { navigationItems, searchFilters } = get();
        let filtered = [...navigationItems];

        if (searchFilters.keyword) {
          const keyword = searchFilters.keyword.toLowerCase();
          filtered = filtered.filter(
            (item) =>
              item.title.toLowerCase().includes(keyword) ||
              item.description?.toLowerCase().includes(keyword) ||
              item.tags?.some((tag) => tag.toLowerCase().includes(keyword))
          );
        }

        if (searchFilters.category) {
          filtered = filtered.filter((item) => item.category === searchFilters.category);
        }

        if (searchFilters.dataCenter) {
          filtered = filtered.filter((item) => item.category === searchFilters.dataCenter);
        }

        if (searchFilters.isFavorite) {
          filtered = filtered.filter((item) => item.isFavorite);
        }

        if (searchFilters.tags && searchFilters.tags.length > 0) {
          filtered = filtered.filter((item) =>
            item.tags?.some((tag) => searchFilters.tags?.includes(tag))
          );
        }

        return filtered;
      },

      // 用户偏好
      setUserPreferences: (preferences) => {
        set((state) => ({
          userPreferences: { ...state.userPreferences, ...preferences },
        }));
      },

      toggleSidebar: () => {
        set((state) => ({
          userPreferences: {
            ...state.userPreferences,
            sidebarCollapsed: !state.userPreferences.sidebarCollapsed,
          },
        }));
      },

      setTheme: (theme) => {
        set((state) => ({
          userPreferences: { ...state.userPreferences, theme },
        }));
      },

      setViewMode: (viewMode) => {
        set((state) => ({
          userPreferences: { ...state.userPreferences, viewMode },
        }));
      },

      // 状态操作
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      // 数据初始化
      initializeData: () => {
        // 初始化示例数据
        const sampleDataCenters: DataCenter[] = [
          {
            id: 'dc1',
            name: '生产环境',
            description: '生产环境相关服务',
            color: '#1890ff',
            icon: 'CloudServer',
            count: 0,
          },
          {
            id: 'dc2',
            name: '测试环境',
            description: '测试环境相关服务',
            color: '#52c41a',
            icon: 'TestTube',
            count: 0,
          },
          {
            id: 'dc3',
            name: '开发环境',
            description: '开发环境相关服务',
            color: '#fa8c16',
            icon: 'Code',
            count: 0,
          },
        ];

        const sampleCategories: NavigationCategory[] = [
          {
            id: 'cat1',
            name: '监控系统',
            description: '系统监控和运维工具',
            color: '#1890ff',
            icon: 'Monitor',
            order: 1,
          },
          {
            id: 'cat2',
            name: '数据平台',
            description: '数据分析和处理平台',
            color: '#722ed1',
            icon: 'Database',
            order: 2,
          },
          {
            id: 'cat3',
            name: '业务系统',
            description: '核心业务应用系统',
            color: '#eb2f96',
            icon: 'Building',
            order: 3,
          },
        ];

        const sampleNavigationItems: NavigationItem[] = [
          {
            id: 'nav1',
            title: 'Grafana监控',
            description: '系统性能和业务指标监控面板',
            url: 'https://grafana.example.com',
            icon: 'LineChart',
            category: 'cat1',
            tags: ['监控', '性能', 'DevOps'],
            isFavorite: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'nav2',
            title: 'Kubernetes Dashboard',
            description: '容器编排管理界面',
            url: 'https://k8s.example.com',
            icon: 'Container',
            category: 'cat1',
            tags: ['K8s', '容器', '运维'],
            isFavorite: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'nav3',
            title: '数据仓库',
            description: '企业级数据仓库管理平台',
            url: 'https://dw.example.com',
            icon: 'Database',
            category: 'cat2',
            tags: ['数据', 'ETL', '分析'],
            isFavorite: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'nav4',
            title: '客户管理系统',
            description: 'CRM客户关系管理系统',
            url: 'https://crm.example.com',
            icon: 'Users',
            category: 'cat3',
            tags: ['CRM', '客户', '销售'],
            isFavorite: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ];

        set({
          dataCenters: sampleDataCenters,
          categories: sampleCategories,
          navigationItems: sampleNavigationItems,
        });
      },
    }),
    {
      name: 'ftnav-storage',
      partialize: (state) => ({
        navigationItems: state.navigationItems,
        dataCenters: state.dataCenters,
        categories: state.categories,
        userPreferences: state.userPreferences,
      }),
    }
  )
);