import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, Tag, ColorPicker, Button, Space, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { NavigationItem } from '../types';
import { useNavigationStore } from '../store/navigationStore';
import { IconComponent, iconMap } from './IconComponent';

interface NavigationFormProps {
  visible: boolean;
  onCancel: () => void;
  editItem?: NavigationItem | null;
}

/**
 * 导航项编辑表单组件
 * 支持添加和编辑导航项功能
 */
export const NavigationForm: React.FC<NavigationFormProps> = ({
  visible,
  onCancel,
  editItem,
}) => {
  const [form] = Form.useForm();
  const { categories, addNavigationItem, updateNavigationItem } = useNavigationStore();

  // 当编辑项变化时，更新表单
  useEffect(() => {
    if (visible) {
      if (editItem) {
        form.setFieldsValue({
          title: editItem.title,
          description: editItem.description,
          url: editItem.url,
          icon: editItem.icon,
          category: editItem.category,
          tags: editItem.tags || [],
        });
      } else {
        form.resetFields();
      }
    }
  }, [editItem, form, visible]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editItem) {
        // 编辑模式
        updateNavigationItem(editItem.id, {
          ...values,
          tags: values.tags || [],
        });
        message.success('导航项已更新');
      } else {
        // 添加模式
        addNavigationItem({
          ...values,
          tags: values.tags || [],
        });
        message.success('导航项已添加');
      }
      
      form.resetFields();
      onCancel();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 图标选项
  const iconOptions = Object.keys(iconMap).map(key => ({
    label: (
      <Space>
        <IconComponent name={iconMap[key as keyof typeof iconMap]} size={16} />
        <span>{key}</span>
      </Space>
    ),
    value: iconMap[key as keyof typeof iconMap],
  }));

  // 分类选项
  const categoryOptions = categories.map(cat => ({
    label: (
      <Space>
        <IconComponent name={cat.icon} size={16} color={cat.color} />
        <span>{cat.name}</span>
      </Space>
    ),
    value: cat.id,
  }));

  return (
    <Modal
      title={editItem ? '编辑导航项' : '添加导航项'}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {editItem ? '更新' : '添加'}
        </Button>,
      ]}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          icon: 'AppstoreOutlined',
          tags: [],
        }}
      >
        <Form.Item
          name="title"
          label="标题"
          rules={[{ required: true, message: '请输入导航项标题' }]}
        >
          <Input placeholder="请输入导航项标题" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
        >
          <Input.TextArea 
            placeholder="请输入导航项描述" 
            rows={2}
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          name="url"
          label="链接地址"
          rules={[
            { required: true, message: '请输入链接地址' },
            { type: 'url', message: '请输入有效的URL地址' },
          ]}
        >
          <Input placeholder="https://example.com" />
        </Form.Item>

        <Form.Item
          name="icon"
          label="图标"
          rules={[{ required: true, message: '请选择图标' }]}
        >
          <Select
            placeholder="请选择图标"
            showSearch
            optionFilterProp="children"
            options={iconOptions}
          />
        </Form.Item>

        <Form.Item
          name="category"
          label="分类"
          rules={[{ required: true, message: '请选择分类' }]}
        >
          <Select
            placeholder="请选择分类"
            options={categoryOptions}
          />
        </Form.Item>

        <Form.Item
          name="tags"
          label="标签"
        >
          <Select
            mode="tags"
            placeholder="输入标签后按回车添加"
            style={{ width: '100%' }}
            tokenSeparators={[',']}
            maxTagCount={5}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default NavigationForm; 