import React from 'react';
import * as AntIcons from '@ant-design/icons';
import * as LucideIcons from 'lucide-react';

interface IconComponentProps {
  name: string;
  size?: number;
  color?: string;
  className?: string;
}

/**
 * 通用图标组件
 * 支持 Ant Design 图标和 Lucide 图标
 */
export const IconComponent: React.FC<IconComponentProps> = ({ 
  name, 
  size = 20, 
  color, 
  className = '' 
}) => {
  // 首先尝试从 Ant Design 图标中查找
  const AntIcon = (AntIcons as any)[name];
  if (AntIcon) {
    return (
      <AntIcon 
        style={{ 
          fontSize: size, 
          color: color 
        }} 
        className={className}
      />
    );
  }

  // 然后尝试从 Lucide 图标中查找
  const LucideIcon = (LucideIcons as any)[name];
  if (LucideIcon) {
    return (
      <LucideIcon 
        size={size} 
        color={color} 
        className={className}
      />
    );
  }

  // 如果都找不到，显示默认图标
  return (
    <AntIcons.AppstoreOutlined 
      style={{ 
        fontSize: size, 
        color: color || '#6B7280' 
      }} 
      className={className}
    />
  );
};

// 预定义的图标映射，用于确保类型安全
export const iconMap = {
  // 通用图标
  Home: 'HomeOutlined',
  Search: 'SearchOutlined',
  Plus: 'PlusOutlined',
  Edit: 'EditOutlined',
  Delete: 'DeleteOutlined',
  Setting: 'SettingOutlined',
  User: 'UserOutlined',
  Star: 'StarOutlined',
  Heart: 'HeartOutlined',
  
  // 业务图标
  Dashboard: 'DashboardOutlined',
  Chart: 'BarChartOutlined',
  LineChart: 'LineChartOutlined',
  PieChart: 'PieChartOutlined',
  Table: 'TableOutlined',
  Database: 'DatabaseOutlined',
  Server: 'ServerOutlined',
  Cloud: 'CloudOutlined',
  CloudServer: 'CloudServerOutlined',
  
  // 开发工具
  Code: 'CodeOutlined',
  Bug: 'BugOutlined',
  Tool: 'ToolOutlined',
  Api: 'ApiOutlined',
  Terminal: 'ConsoleSqlOutlined',
  Git: 'BranchesOutlined',
  
  // 监控运维
  Monitor: 'MonitorOutlined',
  Alert: 'AlertOutlined',
  Safety: 'SafetyOutlined',
  Shield: 'ShieldCheckOutlined',
  Lock: 'LockOutlined',
  Key: 'KeyOutlined',
  
  // 业务系统
  Bank: 'BankOutlined',
  Money: 'DollarOutlined',
  Trade: 'StockOutlined',
  Shop: 'ShopOutlined',
  Customer: 'TeamOutlined',
  Users: 'UsergroupAddOutlined',
  Building: 'BuildingOutlined',
  
  // 数据相关
  Analytics: 'FundProjectionScreenOutlined',
  Report: 'FileTextOutlined',
  Document: 'FileOutlined',
  Export: 'ExportOutlined',
  Import: 'ImportOutlined',
  
  // 网络通信
  Network: 'GlobalOutlined',
  Wifi: 'WifiOutlined',
  Mobile: 'MobileOutlined',
  Mail: 'MailOutlined',
  Message: 'MessageOutlined',
  
  // 存储
  Folder: 'FolderOutlined',
  File: 'FileOutlined',
  Image: 'PictureOutlined',
  Video: 'VideoCameraOutlined',
  Audio: 'AudioOutlined',
  
  // 状态图标
  Success: 'CheckCircleOutlined',
  Warning: 'WarningOutlined',
  Error: 'CloseCircleOutlined',
  Info: 'InfoCircleOutlined',
  
  // Lucide 图标补充
  Container: 'Container',
  TestTube: 'TestTube',
  Zap: 'Zap',
  Layers: 'Layers',
  Package: 'Package',
  Cpu: 'Cpu',
  HardDrive: 'HardDrive',
  Activity: 'Activity',
  TrendingUp: 'TrendingUp',
  BarChart3: 'BarChart3',
  
} as const;

export type IconName = keyof typeof iconMap;

/**
 * 类型安全的图标组件
 */
interface TypedIconProps extends Omit<IconComponentProps, 'name'> {
  name: IconName;
}

export const TypedIcon: React.FC<TypedIconProps> = ({ name, ...props }) => {
  const iconName = iconMap[name];
  return <IconComponent name={iconName} {...props} />;
};

export default IconComponent; 