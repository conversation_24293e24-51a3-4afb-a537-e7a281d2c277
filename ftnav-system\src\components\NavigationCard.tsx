import React, { useState } from 'react';
import { Card, Tag, Tooltip, Dropdown, Modal, message, Button, type MenuProps } from 'antd';
import { 
  HeartOutlined, 
  HeartFilled, 
  MoreOutlined, 
  EditOutlined, 
  DeleteOutlined,
  LinkOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  <PERSON>boltOutlined,
  UserOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import type { NavigationItem } from '../types';
import { useNavigationStore } from '../store/navigationStore';
import { IconComponent } from './IconComponent';

interface NavigationCardProps {
  item: NavigationItem;
  onEdit?: (item: NavigationItem) => void;
  className?: string;
}

/**
 * 应用商店风格的导航卡片组件
 * 采用横向布局，支持收藏、编辑、删除、跳转等功能
 */
export const NavigationCard: React.FC<NavigationCardProps> = ({ 
  item, 
  onEdit,
  className = '' 
}) => {
  const { toggleFavorite, deleteNavigationItem, categories } = useNavigationStore();
  const [isHovered, setIsHovered] = useState(false);

  // 获取分类信息
  const category = categories.find(cat => cat.id === item.category);

  // 处理卡片点击 - 打开链接
  const handleCardClick = (e: React.MouseEvent) => {
    // 防止在点击操作按钮时触发
    if ((e.target as HTMLElement).closest('.store-card-actions') || 
        (e.target as HTMLElement).closest('.store-card-footer')) {
      return;
    }
    
    // 在新标签页中打开链接
    window.open(item.url, '_blank', 'noopener,noreferrer');
  };

  // 处理收藏切换
  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleFavorite(item.id);
    message.success(item.isFavorite ? '已取消收藏' : '已添加到收藏');
  };

  // 处理编辑
  const handleEdit = () => {
    onEdit?.(item);
  };

  // 处理删除
  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除导航项 "${item.title}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        deleteNavigationItem(item.id);
        message.success('导航项已删除');
      },
    });
  };

  // 复制链接
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(item.url);
      message.success('链接已复制到剪贴板');
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = item.url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('链接已复制到剪贴板');
    }
  };

  // 下拉菜单项
  const menuItems: MenuProps['items'] = [
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      onClick: handleEdit,
    },
    {
      key: 'copy',
      label: '复制链接',
      icon: <LinkOutlined />,
      onClick: handleCopyLink,
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: handleDelete,
    },
  ];

  // 生成随机统计数据（实际项目中应该从真实数据获取）
  const generateStats = () => {
    const baseViews = Math.floor(Math.random() * 10000) + 1000;
    const baseLikes = Math.floor(Math.random() * 1000) + 100;
    const baseUsage = Math.floor(Math.random() * 500) + 50;
    
    return {
      views: baseViews,
      likes: baseLikes,
      usage: baseUsage
    };
  };

  const stats = generateStats();

  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div 
      className={`store-card ${className}`}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 主要内容区域 */}
      <div className="store-card-main">
        {/* 应用图标 */}
        <div 
          className="store-card-icon"
          style={{
            background: category?.color 
              ? `linear-gradient(135deg, ${category.color}, ${category.color}dd)`
              : 'linear-gradient(135deg, var(--color-primary), var(--color-primary-hover))'
          }}
        >
          <IconComponent name={item.icon} size={32} color="white" />
        </div>
        
        {/* 应用信息 */}
        <div className="store-card-content">
          <h3 className="store-card-title" title={item.title}>
            {item.title}
          </h3>
          
          <div className="store-card-author">
            <UserOutlined className="store-card-author-icon" />
            <span>@{category?.name || 'FinTech'}</span>
          </div>
          
          {item.description && (
            <p className="store-card-description" title={item.description}>
              {item.description}
            </p>
          )}
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="store-card-actions">
        {/* 收藏按钮 */}
        <Tooltip title={item.isFavorite ? '取消收藏' : '添加收藏'}>
          <div
            className={`store-card-action-btn store-card-favorite ${item.isFavorite ? 'active' : ''}`}
            onClick={handleToggleFavorite}
          >
            {item.isFavorite ? (
              <HeartFilled style={{ fontSize: '14px' }} />
            ) : (
              <HeartOutlined style={{ fontSize: '14px' }} />
            )}
          </div>
        </Tooltip>

        {/* 更多操作 */}
        <Dropdown 
          menu={{ items: menuItems }} 
          trigger={['click']}
          placement="bottomRight"
        >
          <div
            className="store-card-action-btn"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreOutlined style={{ fontSize: '14px' }} />
          </div>
        </Dropdown>


      </div>

      {/* 卡片底部信息 */}
      <div className="store-card-footer">
        {/* 统计信息 */}
        <div className="store-card-stats">
          <div className="store-card-stat">
            <EyeOutlined className="store-card-stat-icon" />
            <span>{formatNumber(stats.views)}</span>
          </div>
          <div className="store-card-stat">
            <ThunderboltOutlined className="store-card-stat-icon" />
            <span>{formatNumber(stats.likes)}</span>
          </div>
          <div className="store-card-stat">
            <UserOutlined className="store-card-stat-icon" />
            <span>{formatNumber(stats.usage)}</span>
          </div>
        </div>

        {/* 标签 */}
        <div className="store-card-tags">
          {item.tags?.slice(0, 2).map((tag, index) => (
            <span 
              key={index}
              className={`store-card-tag ${index === 0 ? 'primary' : ''}`}
            >
              {tag}
            </span>
          ))}
          {item.tags && item.tags.length > 2 && (
            <Tooltip title={item.tags.slice(2).join(', ')}>
              <span className="store-card-tag">
                +{item.tags.length - 2}
              </span>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default NavigationCard; 